namespace Core.Cache.Models
{
    /// <summary>
    /// Cache ayarları configuration modeli - 1000+ salon scalability için optimize edildi
    /// </summary>
    public class CacheSettings
    {
        /// <summary>
        /// Varsayılan TTL (dakika) - 1000+ salon için optimize
        /// </summary>
        public int DefaultTTLMinutes { get; set; } = 30;

        /// <summary>
        /// Maksimum memory kullanımı (MB) - 1000+ salon için optimize
        /// </summary>
        public int MaxMemoryMB { get; set; } = 1024;

        /// <summary>
        /// Cache key prefix - multi-tenant isolation için
        /// </summary>
        public string KeyPrefix { get; set; } = "gymkod";

        /// <summary>
        /// Compression aktif mi - bandwidth optimization için
        /// </summary>
        public bool EnableCompression { get; set; } = false;

        /// <summary>
        /// Logging aktif mi - performance monitoring için
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// Batch operation size limit - 1000+ salon için
        /// </summary>
        public int MaxBatchSize { get; set; } = 100;

        /// <summary>
        /// Connection pool size - 1000+ salon için
        /// </summary>
        public int ConnectionPoolSize { get; set; } = 10;

        /// <summary>
        /// Cache operation timeout (ms) - 1000+ salon için
        /// </summary>
        public int OperationTimeoutMs { get; set; } = 2000;

        /// <summary>
        /// Enterprise scalability settings
        /// </summary>
        public int ConnectionPoolSize { get; set; } = 10;
        public int MaxRetryAttempts { get; set; } = 3;
        public int CircuitBreakerThreshold { get; set; } = 5;
        public int CircuitBreakerTimeoutSeconds { get; set; } = 30;
        public int HealthCheckIntervalSeconds { get; set; } = 10;
        public int CacheTimeoutSeconds { get; set; } = 5;
        public bool EnableMetrics { get; set; } = true;
        public bool EnableDistributedLocking { get; set; } = false;
    }

    /// <summary>
    /// Cache istatistikleri
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// Toplam cache hit sayısı
        /// </summary>
        public long TotalHits { get; set; }

        /// <summary>
        /// Toplam cache miss sayısı
        /// </summary>
        public long TotalMisses { get; set; }

        /// <summary>
        /// Cache hit oranı (%)
        /// </summary>
        public double HitRatio => TotalHits + TotalMisses > 0 
            ? (double)TotalHits / (TotalHits + TotalMisses) * 100 
            : 0;

        /// <summary>
        /// Toplam set işlemi sayısı
        /// </summary>
        public long TotalSets { get; set; }

        /// <summary>
        /// Toplam delete işlemi sayısı
        /// </summary>
        public long TotalDeletes { get; set; }

        /// <summary>
        /// Son reset tarihi
        /// </summary>
        public DateTime LastResetDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Ortalama response time (ms)
        /// </summary>
        public double AverageResponseTimeMs { get; set; }

        /// <summary>
        /// Aktif key sayısı
        /// </summary>
        public long ActiveKeys { get; set; }

        /// <summary>
        /// Kullanılan memory (MB)
        /// </summary>
        public double UsedMemoryMB { get; set; }
    }

    /// <summary>
    /// Cache operation result
    /// </summary>
    public class CacheOperationResult<T>
    {
        /// <summary>
        /// İşlem başarılı mı
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Cache'den geldi mi (hit/miss)
        /// </summary>
        public bool IsFromCache { get; set; }

        /// <summary>
        /// Veri
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Hata mesajı
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// İşlem süresi (ms)
        /// </summary>
        public double ExecutionTimeMs { get; set; }

        /// <summary>
        /// Cache key
        /// </summary>
        public string? CacheKey { get; set; }

        /// <summary>
        /// TTL (saniye)
        /// </summary>
        public int? TTLSeconds { get; set; }
    }

    /// <summary>
    /// Cache invalidation options
    /// </summary>
    public class CacheInvalidationOptions
    {
        /// <summary>
        /// Company ID
        /// </summary>
        public int CompanyId { get; set; }

        /// <summary>
        /// Entity adları (Member, Membership, etc.)
        /// </summary>
        public List<string> EntityNames { get; set; } = new();

        /// <summary>
        /// Pattern'ler (wildcard destekli)
        /// </summary>
        public List<string> Patterns { get; set; } = new();

        /// <summary>
        /// Specific key'ler
        /// </summary>
        public List<string> SpecificKeys { get; set; } = new();

        /// <summary>
        /// Tüm company cache'ini temizle
        /// </summary>
        public bool ClearAllCompanyCache { get; set; } = false;
    }
}
