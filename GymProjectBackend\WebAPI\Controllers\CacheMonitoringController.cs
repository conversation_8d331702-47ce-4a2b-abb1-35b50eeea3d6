using Core.Cache.Abstract;
using Core.Cache.Models;
using Core.Utilities.Results;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Cache monitoring ve performance dashboard
    /// 1000+ salon için enterprise-level monitoring - ÜCRETSIZ!
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CacheMonitoringController : ControllerBase
    {
        private readonly IRedisService _redisService;
        private readonly IRedisConnectionService _connectionService;

        public CacheMonitoringController(
            IRedisService redisService,
            IRedisConnectionService connectionService)
        {
            _redisService = redisService;
            _connectionService = connectionService;
        }

        /// <summary>
        /// Real-time cache dashboard data
        /// </summary>
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboard()
        {
            try
            {
                var stats = await _redisService.GetStatisticsAsync();
                var isHealthy = await _redisService.IsHealthyAsync();
                
                // Memory info
                var process = Process.GetCurrentProcess();
                var serverMemory = GC.GetTotalMemory(false) / 1024 / 1024; // MB

                var dashboard = new
                {
                    // Cache Performance
                    CacheStats = new
                    {
                        TotalHits = stats.TotalHits,
                        TotalMisses = stats.TotalMisses,
                        HitRatio = stats.TotalHits + stats.TotalMisses > 0 
                            ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100 
                            : 0,
                        TotalOperations = stats.TotalHits + stats.TotalMisses + stats.TotalSets + stats.TotalDeletes,
                        AverageResponseTime = stats.AverageResponseTimeMs
                    },
                    
                    // System Health
                    Health = new
                    {
                        RedisConnected = isHealthy,
                        ServerMemoryMB = serverMemory,
                        ProcessMemoryMB = process.WorkingSet64 / 1024 / 1024,
                        Uptime = DateTime.Now - Process.GetCurrentProcess().StartTime,
                        LastResetDate = stats.LastResetDate
                    },
                    
                    // Performance Indicators
                    Performance = new
                    {
                        OperationsPerSecond = CalculateOpsPerSecond(stats),
                        MemoryEfficiency = CalculateMemoryEfficiency(stats, serverMemory),
                        ResponseTimeGrade = GetResponseTimeGrade(stats.AverageResponseTimeMs),
                        OverallGrade = GetOverallGrade(stats, isHealthy)
                    },
                    
                    Timestamp = DateTime.Now
                };

                return Ok(new SuccessDataResult<object>(dashboard, "Cache dashboard data"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Dashboard error: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache performance trends (son 24 saat)
        /// </summary>
        [HttpGet("trends")]
        public async Task<IActionResult> GetTrends()
        {
            try
            {
                // Bu basit implementasyon - production'da time-series DB kullanılabilir
                var stats = await _redisService.GetStatisticsAsync();
                
                var trends = new
                {
                    HourlyStats = GenerateHourlyMockData(stats),
                    Recommendations = GenerateRecommendations(stats),
                    Alerts = GenerateAlerts(stats)
                };

                return Ok(new SuccessDataResult<object>(trends, "Cache trends data"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Trends error: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache health check endpoint
        /// </summary>
        [HttpGet("health")]
        public async Task<IActionResult> HealthCheck()
        {
            try
            {
                var isHealthy = await _connectionService.TestConnectionAsync();
                var stats = await _redisService.GetStatisticsAsync();

                var health = new
                {
                    Status = isHealthy ? "Healthy" : "Unhealthy",
                    RedisConnected = isHealthy,
                    TotalOperations = stats.TotalHits + stats.TotalMisses + stats.TotalSets,
                    HitRatio = stats.TotalHits + stats.TotalMisses > 0 
                        ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100 
                        : 0,
                    AverageResponseTime = stats.AverageResponseTimeMs,
                    CheckTime = DateTime.Now
                };

                return isHealthy 
                    ? Ok(new SuccessDataResult<object>(health, "Cache is healthy"))
                    : StatusCode(503, new ErrorDataResult<object>(health, "Cache is unhealthy"));
            }
            catch (Exception ex)
            {
                return StatusCode(503, new ErrorDataResult<object>(null, $"Health check failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache statistics reset
        /// </summary>
        [HttpPost("reset-stats")]
        public async Task<IActionResult> ResetStatistics()
        {
            try
            {
                await _redisService.ResetStatisticsAsync();
                return Ok(new SuccessResult("Cache statistics reset"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Reset failed: {ex.Message}"));
            }
        }

        #region Helper Methods

        private double CalculateOpsPerSecond(CacheStatistics stats)
        {
            var uptime = DateTime.Now - stats.LastResetDate;
            var totalOps = stats.TotalHits + stats.TotalMisses + stats.TotalSets + stats.TotalDeletes;
            
            return uptime.TotalSeconds > 0 ? totalOps / uptime.TotalSeconds : 0;
        }

        private double CalculateMemoryEfficiency(CacheStatistics stats, long serverMemoryMB)
        {
            // Basit efficiency calculation
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0 
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) 
                : 0;
            
            return hitRatio * 100; // Hit ratio as efficiency
        }

        private string GetResponseTimeGrade(double avgResponseTime)
        {
            return avgResponseTime switch
            {
                < 1 => "A+",
                < 5 => "A",
                < 10 => "B",
                < 50 => "C",
                < 100 => "D",
                _ => "F"
            };
        }

        private string GetOverallGrade(CacheStatistics stats, bool isHealthy)
        {
            if (!isHealthy) return "F";
            
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0 
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;
            
            return hitRatio switch
            {
                >= 90 => "A+",
                >= 80 => "A",
                >= 70 => "B",
                >= 60 => "C",
                >= 50 => "D",
                _ => "F"
            };
        }

        private object[] GenerateHourlyMockData(CacheStatistics stats)
        {
            // Mock data - production'da gerçek time-series data kullanılır
            var data = new List<object>();
            var now = DateTime.Now;
            
            for (int i = 23; i >= 0; i--)
            {
                var hour = now.AddHours(-i);
                data.Add(new
                {
                    Hour = hour.ToString("HH:mm"),
                    Hits = Random.Shared.Next(100, 1000),
                    Misses = Random.Shared.Next(10, 100),
                    ResponseTime = Random.Shared.NextDouble() * 10
                });
            }
            
            return data.ToArray();
        }

        private string[] GenerateRecommendations(CacheStatistics stats)
        {
            var recommendations = new List<string>();
            
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0 
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;
            
            if (hitRatio < 70)
                recommendations.Add("Cache hit ratio düşük. TTL değerlerini artırın.");
            
            if (stats.AverageResponseTimeMs > 10)
                recommendations.Add("Response time yüksek. Redis connection pool'u optimize edin.");
            
            if (recommendations.Count == 0)
                recommendations.Add("Cache performansı optimal durumda!");
            
            return recommendations.ToArray();
        }

        private string[] GenerateAlerts(CacheStatistics stats)
        {
            var alerts = new List<string>();
            
            if (stats.AverageResponseTimeMs > 50)
                alerts.Add("CRITICAL: Response time çok yüksek!");
            
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0 
                ? (double)stats.TotalHits / (stats.TotalMisses + stats.TotalMisses) * 100
                : 0;
            
            if (hitRatio < 50)
                alerts.Add("WARNING: Cache hit ratio çok düşük!");
            
            return alerts.ToArray();
        }

        #endregion
    }
}
