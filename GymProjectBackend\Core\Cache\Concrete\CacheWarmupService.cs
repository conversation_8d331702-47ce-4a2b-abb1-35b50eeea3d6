using Core.Cache.Abstract;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Core.Cache.Concrete
{
    /// <summary>
    /// Cache warmup service - Startup'ta kritik data'yı cache'ler
    /// 1000+ salon için performance optimization
    /// </summary>
    public class CacheWarmupService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<CacheWarmupService> _logger;
        private readonly IRedisService _redisService;

        public CacheWarmupService(
            IServiceProvider serviceProvider,
            ILogger<CacheWarmupService> logger,
            IRedisService redisService)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _redisService = redisService;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Cache warmup başlatılıyor...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                
                // Static data'ları cache'le - 1000+ salon için kritik!
                await WarmupStaticData(scope);
                
                // Frequently accessed data'ları cache'le
                await WarmupFrequentData(scope);

                _logger.LogInformation("Cache warmup tamamlandı");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache warmup sırasında hata oluştu");
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Cache warmup service durduruluyor");
            return Task.CompletedTask;
        }

        private async Task WarmupStaticData(IServiceScope scope)
        {
            try
            {
                // City ve Town data'ları - 24 saat cache, tüm salonlar kullanır
                var cityService = scope.ServiceProvider.GetService<Business.Abstract.ICityService>();
                var townService = scope.ServiceProvider.GetService<Business.Abstract.ITownService>();

                if (cityService != null)
                {
                    var cities = cityService.GetAll();
                    _logger.LogDebug("Cities cache'lendi: {Count} şehir", cities.Data?.Count ?? 0);
                }

                if (townService != null)
                {
                    var towns = townService.GetAll();
                    _logger.LogDebug("Towns cache'lendi: {Count} ilçe", towns.Data?.Count ?? 0);
                }

                // Operation Claims - Tüm salonlar için aynı
                var operationClaimService = scope.ServiceProvider.GetService<Business.Abstract.IOperationClaimService>();
                if (operationClaimService != null)
                {
                    var claims = operationClaimService.GetAll();
                    _logger.LogDebug("Operation claims cache'lendi: {Count} yetki", claims.Data?.Count ?? 0);
                }

                // License Packages - Owner'lar için kritik
                var licensePackageService = scope.ServiceProvider.GetService<Business.Abstract.ILicensePackageService>();
                if (licensePackageService != null)
                {
                    var packages = licensePackageService.GetAll();
                    _logger.LogDebug("License packages cache'lendi: {Count} paket", packages.Data?.Count ?? 0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Static data warmup hatası");
            }
        }

        private async Task WarmupFrequentData(IServiceScope scope)
        {
            try
            {
                // Bu method'da company-specific data'ları cache'leyebiliriz
                // Ama startup'ta company context olmadığı için şimdilik boş bırakıyoruz
                // Runtime'da first-access'te cache'lenecek

                _logger.LogDebug("Frequent data warmup tamamlandı");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Frequent data warmup hatası");
            }
        }
    }
}
